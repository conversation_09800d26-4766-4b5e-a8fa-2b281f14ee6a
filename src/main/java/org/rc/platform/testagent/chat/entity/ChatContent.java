package org.rc.platform.testagent.chat.entity;

import java.util.Date;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * AI会话内容表(ChatContent)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Data
@TableName(value = "ai_chat_content")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class ChatContent extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 重写父类字段，标记为数据库中不存在
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private Long conversationId;
    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private Long appId;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String modelType;
    /**
     * 命令类型
     */
    @Schema(description = "命令类型")
    private Integer commandType;
    /**
     * 工具指令
     */
    @Schema(description = "工具指令")
    private Integer toolsCommand;
    /**
     * 用户输入
     */
    @Schema(description = "用户输入")
    private String inContent;
    /**
     * 用户输入时间
     */
    @Schema(description = "用户输入时间")
    private Date inContentTime;
    /**
     * 是否命令
     */
    @Schema(description = "是否命令")
    private String isCommand;
    /**
     * 命令的具体内容
     */
    @Schema(description = "命令的具体内容")
    private String commandContent;
    /**
     * 用户的确认状态
     */
    @Schema(description = "用户的确认状态")
    private String userConfirmation;
    /**
     * 确认命令的时间
     */
    @Schema(description = "确认命令的时间")
    private Date confirmationTime;
    /**
     * 模型输出数据
     */
    @Schema(description = "模型输出数据")
    private Date outContentTime;
    /**
     * 模型输出
     */
    @Schema(description = "模型输出")
    private String outContent;
    /**
     * 输出文本类型0 普通文本|1富文本
     */
    @Schema(description = "输出文本类型0 普通文本|1富文本")
    private Integer outContentType;
    /**
     * 大模型总输出字数
     */
    @Schema(description = "大模型总输出字数")
    private Integer modelTextSize;
    /**
     * 大模型输入token数
     */
    @Schema(description = "大模型输入token数")
    private Integer modelInputTokens;
    /**
     * 大模型输出token数
     */
    @Schema(description = "大模型输出token数")
    private Integer modelOutputTokens;
    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String prompt;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 扩展属性77 (如果数据库中存在此字段)
     */
    @Schema(description = "扩展属性77")
    @TableField(value = "Attribute_77")
    private String attribute77;

}
