package org.rc.platform.testagent.chat.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * AI会话信息表(ChatConversation)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Data
@TableName(value = "ai_chat_conversation")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class ChatConversation extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private Long appId;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
