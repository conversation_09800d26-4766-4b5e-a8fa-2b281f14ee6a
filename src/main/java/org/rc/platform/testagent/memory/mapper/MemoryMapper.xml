<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.memory.mapper.MemoryMapper">

    <resultMap type="org.rc.platform.testagent.memory.entity.Memory" id="memoryResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="conversationId" column="conversation_id" jdbcType="INTEGER"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="importanceScore" column="importance_score" jdbcType="VARCHAR"/>
        <result property="relevanceScore" column="relevance_score" jdbcType="VARCHAR"/>
        <result property="isCritical" column="is_critical" jdbcType="INTEGER"/>
        <result property="memoryType" column="memory_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_memory(conversation_id, role, content, importance_score, relevance_score, is_critical,
        memory_type, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.role}, #{entity.content}, #{entity.importanceScore},
            #{entity.relevanceScore}, #{entity.isCritical}, #{entity.memoryType}, #{entity.createTime},
            #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.isDeleted}, #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_memory(conversation_id, role, content, importance_score, relevance_score, is_critical,
        memory_type, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.role}, #{entity.content}, #{entity.importanceScore},
            #{entity.relevanceScore}, #{entity.isCritical}, #{entity.memoryType}, #{entity.createTime},
            #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.isDeleted}, #{entity.status})
        </foreach>
        on duplicate key update
        conversation_id = values(conversation_id) , role = values(role) , content = values(content) , importance_score =
        values(importance_score) , relevance_score = values(relevance_score) , is_critical = values(is_critical) ,
        memory_type = values(memory_type) , create_time = values(create_time) , update_time = values(update_time) ,
        tenant_id = values(tenant_id) , version = values(version) , create_user = values(create_user) , update_user =
        values(update_user) , is_deleted = values(is_deleted) , status = values(status)
    </insert>
    <select id="selectMemoryPage" resultMap="memoryResultMap">
        select *
        from ai_memory
        where is_deleted = 0
    </select>

</mapper>
