#服务器端口
server:
  port: 9303

#数据源配置
spring:
  datasource:
    url: ${richinfo.datasource.dev.url}
    username: ${richinfo.datasource.dev.username}
    password: ${richinfo.datasource.dev.password}

rocketmq:
  name-server: localhost:9876 # mq地址
  producer:
    group: test_agent_producer # 必须指定group
    send-message-timeout: 3000 # 消息发送超时时长，默认3s
    retry-times-when-send-failed: 3 # 同步发送消息失败重试次数，默认2
    retry-times-when-send-async-failed: 3 # 异步发送消息失败重试次数，默认2
    customized-trace-topic: TEST\_\_TOPIC

# 云AI配置
yun:
  ai:
    #text-llm-url: http://127.0.0.1:19193/api/text/llm/chat
    text-llm-url: http://************:8883/ai-test/api/text/llm/chat
    text-llm-appkey: 1051258907156733952
    text-llm-app-secret-id: 1166592312499274019
    text-llm-app-secret: Hy)dhhWFsXrKdyrt
